#!/usr/bin/env python3
"""
Script to extract CHD file URLs from the Archive.org CHD-PSX-USA directory listing
"""

import re
import requests
from urllib.parse import urljoin

def extract_chd_urls(html_content, base_url):
    """Extract CHD file URLs from the HTML content"""
    urls = []

    # Multiple patterns to try
    patterns = [
        r'href="([^"]*\.chd)"',  # Standard href with .chd
        r'<a[^>]*href="([^"]*\.chd)"[^>]*>',  # More specific anchor tag pattern
        r'href=\'([^\']*\.chd)\'',  # Single quotes
    ]

    # Also try to find .chd files in the text content
    # Look for lines that contain .chd filenames
    chd_files = re.findall(r'([A-Za-z0-9\s\-\.\(\)!,&\':]+\.chd)', html_content)

    # Try each pattern
    for pattern in patterns:
        matches = re.findall(pattern, html_content, re.IGNORECASE)
        for match in matches:
            full_url = urljoin(base_url, match)
            if full_url not in urls:
                urls.append(full_url)

    # If no direct href matches, construct URLs from filenames found in text
    if not urls and chd_files:
        print(f"No direct href matches found, constructing URLs from {len(chd_files)} filenames...")
        for filename in chd_files:
            # Clean up the filename (remove extra whitespace, etc.)
            clean_filename = filename.strip()
            if clean_filename.endswith('.chd'):
                full_url = urljoin(base_url, clean_filename)
                if full_url not in urls:
                    urls.append(full_url)

    return urls

def main():
    base_url = "https://myrient.erista.me/files/Internet%20Archive/chadmaster/chd_psx/CHD-PSX-USA/"
    
    print("Fetching webpage content...")
    response = requests.get(base_url)
    response.raise_for_status()
    
    print("Extracting CHD file URLs...")

    # Debug: Save the HTML content to see what we're working with
    with open('debug_html.txt', 'w', encoding='utf-8') as f:
        f.write(response.text)
    print("HTML content saved to debug_html.txt for inspection")

    urls = extract_chd_urls(response.text, base_url)

    print(f"Found {len(urls)} CHD file URLs")
    
    # Save URLs to file
    with open('chd_urls.txt', 'w') as f:
        for url in urls:
            f.write(url + '\n')
    
    print("URLs saved to chd_urls.txt")
    
    # Also print first few URLs as preview
    print("\nFirst 10 URLs:")
    for i, url in enumerate(urls[:10]):
        print(f"{i+1}. {url}")

if __name__ == "__main__":
    main()
